import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
// Note: tauri-plugin-geolocation is primarily for mobile platforms
// For desktop, we'll rely on the browser's geolocation API

interface GeolocationPosition {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

// This interface should match the Rust struct
interface GpsLocation {
  latitude: number;
  longitude: number;
  accuracy?: number;
  address?: string;
}

interface GeolocationError {
  code: number;
  message: string;
}

interface UseGeolocationReturn {
  position: GeolocationPosition | null;
  error: GeolocationError | null;
  formattedAddress: string;
  isLoading: boolean;
  getPosition: () => Promise<void>;
  permissionStatus: PermissionState | null;
  requestPermission: () => Promise<void>;
}

export function useGeolocation(): UseGeolocationReturn {
  const [position, setPosition] = useState<GeolocationPosition | null>(null);
  const [error, setError] = useState<GeolocationError | null>(null);
  const [formattedAddress, setFormattedAddress] = useState<string>('Determining location...');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [permissionStatus, setPermissionStatus] = useState<PermissionState | null>(null);

  // Function to request geolocation permission
  const requestPermission = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      // Check if we're in a Tauri context (desktop app)
      const isTauri = typeof window !== 'undefined' && '__TAURI__' in window;
      console.log('🔍 Environment detection:', { isTauri, userAgent: navigator.userAgent });

      if (isTauri) {
        console.log('🖥️ Running in Tauri desktop app - using headless browser geolocation');
        await getPosition(); // This will use the headless browser approach
        return;
      }

      console.log('🌐 Running in web browser - using standard geolocation API');

      // Set a timeout to prevent indefinite loading
      const timeoutId = setTimeout(() => {
        if (isLoading) {
          setIsLoading(false);
          setError({
            code: 999,
            message: 'Location request timed out. Please try again.'
          });
        }
      }, 10000); // 10 second timeout
      
      // First, try to get the last known location from our Rust backend
      try {
        const lastLocation = await invoke<GpsLocation | null>('get_last_location');
        if (lastLocation) {
          // Convert GpsLocation to GeolocationPosition
          const position: GeolocationPosition = {
            latitude: lastLocation.latitude,
            longitude: lastLocation.longitude,
            accuracy: lastLocation.accuracy || 0,
            timestamp: Date.now() // Use current timestamp since we don't have one from storage
          };
          
          setPosition(position);
          setFormattedAddress(lastLocation.address ||
            `Location at ${lastLocation.latitude.toFixed(6)}, ${lastLocation.longitude.toFixed(6)}`);
          setIsLoading(false);
          clearTimeout(timeoutId);
          return;
        }
      } catch (err) {
        console.log('No last location available:', err);
        // Continue with browser geolocation
      }
      
      // Show a message explaining why we need location permission
      try {
        const message = await invoke<string>('show_location_permission_dialog');
        console.log("Location permission message:", message);
        // You could display this message in a UI component if needed
      } catch (err) {
        console.error("Failed to show location permission dialog:", err);
      }
      
      if (navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'geolocation' as PermissionName });
        setPermissionStatus(permission.state);
        
        // Listen for permission changes
        permission.addEventListener('change', () => {
          setPermissionStatus(permission.state);
          if (permission.state === 'granted') {
            getPosition();
          }
        });

        if (permission.state === 'granted') {
          getPosition();
          clearTimeout(timeoutId);
        } else if (permission.state === 'prompt') {
          // This will trigger the browser's permission prompt
          navigator.geolocation.getCurrentPosition(
            () => {
              // Success callback
              setPermissionStatus('granted');
              getPosition();
              clearTimeout(timeoutId);
            },
            (err) => {
              // Error callback
              console.error('Permission prompt error:', err);
              setPermissionStatus('denied');
              setError({
                code: err.code || 1,
                message: err.message || 'Location access denied'
              });
              setIsLoading(false);
              clearTimeout(timeoutId);
            },
            { timeout: 5000 }
          );
        } else if (permission.state === 'denied') {
          setError({
            code: 1,
            message: 'Geolocation permission denied. Please enable location access in your browser settings.'
          });
          setIsLoading(false);
          clearTimeout(timeoutId);
        }
      } else {
        // Fallback for browsers that don't support the Permissions API
        getPosition();
        clearTimeout(timeoutId);
      }
    } catch (err) {
      console.error('Error requesting permission:', err);
      // Fallback to direct geolocation request
      getPosition();
      setIsLoading(false);
    }
  };

  // Function to get the current position
  const getPosition = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    // Set a timeout to prevent indefinite loading
    const timeoutId = setTimeout(() => {
      if (isLoading) {
        setIsLoading(false);
        setError({
          code: 999,
          message: 'Location request timed out. Please try again.'
        });
        setFormattedAddress('Location unavailable');
      }
    }, 20000); // 20 second timeout for headless browser

    try {
      // Check if we're in a Tauri context (desktop app)
      const isTauri = typeof window !== 'undefined' && '__TAURI__' in window;
      console.log('🔍 getPosition() - Environment check:', {
        isTauri,
        hasTauriGlobal: '__TAURI__' in window,
        windowKeys: Object.keys(window).filter(k => k.includes('tauri') || k.includes('TAURI'))
      });

      let position: GeolocationPosition;

      if (isTauri) {
        // Use headless browser approach for desktop
        console.log('🖥️ Using headless browser geolocation for desktop app...');

        try {
          console.log('📞 Calling get_location_headless command...');
          const location = await invoke<GpsLocation>('get_location_headless');
          console.log('✅ Headless browser geolocation success:', location);

          position = {
            latitude: location.latitude,
            longitude: location.longitude,
            accuracy: location.accuracy || 0,
            timestamp: Date.now(),
          };
        } catch (headlessErr) {
          console.error('❌ Headless browser geolocation failed:', headlessErr);
          throw {
            code: 1,
            message: `Desktop geolocation failed: ${headlessErr}. Please ensure location services are enabled on your system.`
          };
        }
      } else {
        // Use browser geolocation API for web
        console.log('🌐 Using browser geolocation API for web...');

        position = await new Promise<GeolocationPosition>((resolve, reject) => {
          // Check if geolocation is available
          if (!navigator.geolocation) {
            console.error('❌ Navigator.geolocation not available');
            reject({
              code: 2,
              message: 'Geolocation is not supported by this browser or system.'
            });
            return;
          }

          console.log('📍 Requesting browser geolocation...');
          navigator.geolocation.getCurrentPosition(
            (pos) => {
              console.log('✅ Browser geolocation success:', pos);
              resolve({
                latitude: pos.coords.latitude,
                longitude: pos.coords.longitude,
                accuracy: pos.coords.accuracy,
                timestamp: pos.timestamp,
              });
            },
            (err) => {
              console.error('❌ Browser geolocation error:', err);

              // Provide more specific error messages for web context
              let message = err.message || 'Unable to retrieve your location';
              if (err.code === 1) {
                message = 'Location access denied. Please check your browser location settings and allow location access for this application.';
              } else if (err.code === 2) {
                message = 'Location information is unavailable. Please check your internet connection and location services.';
              } else if (err.code === 3) {
                message = 'Location request timed out. Please try again.';
              }

              reject({
                code: err.code,
                message: message,
              });
            },
            {
              enableHighAccuracy: true,
              timeout: 8000,  // 8 second timeout for the geolocation request
              maximumAge: 5000 // Use cached position if it's less than 5 seconds old
            }
          );
        });
      }

      // Clear the timeout since we got a position
      clearTimeout(timeoutId);
      
      setPosition(position);

      try {
        // Get a formatted address from the backend
        const address = await invoke<string>('get_formatted_address', {
          latitude: position.latitude,
          longitude: position.longitude,
        });

        setFormattedAddress(address);
        
        // Send the location to the Rust backend
        await invoke('save_location', {
          location: {
            latitude: position.latitude,
            longitude: position.longitude,
            accuracy: position.accuracy,
            address: address,
          },
        });
      } catch (backendErr) {
        console.error('Error with backend location services:', backendErr);
        // Even if backend fails, we still have the position
        const fallbackAddress = `Location at ${position.latitude.toFixed(6)}, ${position.longitude.toFixed(6)}`;
        setFormattedAddress(fallbackAddress);
        
        // Try to save even with the fallback address
        try {
          await invoke('save_location', {
            location: {
              latitude: position.latitude,
              longitude: position.longitude,
              accuracy: position.accuracy,
              address: fallbackAddress,
            },
          });
        } catch (saveErr) {
          console.error('Failed to save location to backend:', saveErr);
        }
      }
    } catch (err: any) {
      console.error('Error getting location:', err);
      clearTimeout(timeoutId);
      
      // Provide more user-friendly error messages
      let errorMessage = 'Location unavailable';
      if (err.code === 1) {
        errorMessage = 'Location access denied. Please check your privacy settings.';
        // Update permission status if denied
        setPermissionStatus('denied');
      } else if (err.code === 2) {
        errorMessage = 'Location unavailable. Please check your device settings.';
      } else if (err.code === 3) {
        errorMessage = 'Location request timed out. Please try again.';
      }
      
      setError({
        code: err.code || 0,
        message: errorMessage
      });
      setFormattedAddress('Location unavailable');
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize on component mount
  useEffect(() => {
    // Request permission on component mount
    requestPermission();
    
    // We don't need to set up a watch position here since we're handling
    // position updates manually with getPosition
    
    // Clean up function
    return () => {
      // Clear any watchers or listeners if needed
      if (navigator.geolocation) {
        // Clear any active watchers - note that 0 is not a valid watch ID
        // We would need to store the actual watch ID if we were using watchPosition
      }
    };
  }, []);

  return {
    position,
    error,
    formattedAddress,
    isLoading,
    getPosition,
    permissionStatus,
    requestPermission
  };
}