import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { getCurrentPosition } from '@tauri-apps/plugin-geolocation';

interface GeolocationPosition {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

// This interface should match the Rust struct
interface GpsLocation {
  latitude: number;
  longitude: number;
  accuracy?: number;
  address?: string;
}

interface GeolocationError {
  code: number;
  message: string;
}

interface UseGeolocationReturn {
  position: GeolocationPosition | null;
  error: GeolocationError | null;
  formattedAddress: string;
  isLoading: boolean;
  getPosition: () => Promise<void>;
  permissionStatus: PermissionState | null;
  requestPermission: () => Promise<void>;
}

export function useGeolocation(): UseGeolocationReturn {
  const [position, setPosition] = useState<GeolocationPosition | null>(null);
  const [error, setError] = useState<GeolocationError | null>(null);
  const [formattedAddress, setFormattedAddress] = useState<string>('Determining location...');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [permissionStatus, setPermissionStatus] = useState<PermissionState | null>(null);

  // Function to request geolocation permission
  const requestPermission = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Set a timeout to prevent indefinite loading
      const timeoutId = setTimeout(() => {
        if (isLoading) {
          setIsLoading(false);
          setError({
            code: 999,
            message: 'Location request timed out. Please try again.'
          });
        }
      }, 10000); // 10 second timeout
      
      // First, try to get the last known location from our Rust backend
      try {
        const lastLocation = await invoke<GpsLocation | null>('get_last_location');
        if (lastLocation) {
          // Convert GpsLocation to GeolocationPosition
          const position: GeolocationPosition = {
            latitude: lastLocation.latitude,
            longitude: lastLocation.longitude,
            accuracy: lastLocation.accuracy || 0,
            timestamp: Date.now() // Use current timestamp since we don't have one from storage
          };
          
          setPosition(position);
          setFormattedAddress(lastLocation.address ||
            `Location at ${lastLocation.latitude.toFixed(6)}, ${lastLocation.longitude.toFixed(6)}`);
          setIsLoading(false);
          clearTimeout(timeoutId);
          return;
        }
      } catch (err) {
        console.log('No last location available:', err);
        // Continue with browser geolocation
      }
      
      // Show a message explaining why we need location permission
      try {
        const message = await invoke<string>('show_location_permission_dialog');
        console.log("Location permission message:", message);
        // You could display this message in a UI component if needed
      } catch (err) {
        console.error("Failed to show location permission dialog:", err);
      }
      
      if (navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'geolocation' as PermissionName });
        setPermissionStatus(permission.state);
        
        // Listen for permission changes
        permission.addEventListener('change', () => {
          setPermissionStatus(permission.state);
          if (permission.state === 'granted') {
            getPosition();
          }
        });

        if (permission.state === 'granted') {
          getPosition();
          clearTimeout(timeoutId);
        } else if (permission.state === 'prompt') {
          // This will trigger the browser's permission prompt
          navigator.geolocation.getCurrentPosition(
            () => {
              // Success callback
              setPermissionStatus('granted');
              getPosition();
              clearTimeout(timeoutId);
            },
            (err) => {
              // Error callback
              console.error('Permission prompt error:', err);
              setPermissionStatus('denied');
              setError({
                code: err.code || 1,
                message: err.message || 'Location access denied'
              });
              setIsLoading(false);
              clearTimeout(timeoutId);
            },
            { timeout: 5000 }
          );
        } else if (permission.state === 'denied') {
          setError({
            code: 1,
            message: 'Geolocation permission denied. Please enable location access in your browser settings.'
          });
          setIsLoading(false);
          clearTimeout(timeoutId);
        }
      } else {
        // Fallback for browsers that don't support the Permissions API
        getPosition();
        clearTimeout(timeoutId);
      }
    } catch (err) {
      console.error('Error requesting permission:', err);
      // Fallback to direct geolocation request
      getPosition();
      setIsLoading(false);
    }
  };

  // Function to get the current position
  const getPosition = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    // Set a timeout to prevent indefinite loading
    const timeoutId = setTimeout(() => {
      if (isLoading) {
        setIsLoading(false);
        setError({
          code: 999,
          message: 'Location request timed out. Please try again.'
        });
        setFormattedAddress('Location unavailable');
      }
    }, 10000); // 10 second timeout

    try {
      // Try to use the Tauri geolocation plugin first
      let position: GeolocationPosition;
      
      try {
        console.log('Attempting to use Tauri geolocation plugin...');
        // Use the Tauri plugin for desktop app
        const pos = await getCurrentPosition();
        console.log('Tauri geolocation plugin success:', pos);
        position = {
          latitude: pos.coords.latitude,
          longitude: pos.coords.longitude,
          accuracy: pos.coords.accuracy,
          timestamp: pos.timestamp,
        };
      } catch (pluginErr) {
        console.error('Tauri plugin failed with error:', pluginErr);
        
        // Fall back to browser API if plugin fails
        console.log('Falling back to browser geolocation API...');
        
        // Check if we're in a Tauri context
        const isTauri = typeof window !== 'undefined' && 'Tauri' in window;
        
        if (isTauri) {
          console.log('Running in Tauri context, showing error');
          throw {
            code: 1,
            message: 'Location access denied. Please check your system settings and ensure location permissions are granted to this application.'
          };
        }
        
        position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(
            (pos) => {
              console.log('Browser geolocation success');
              resolve({
                latitude: pos.coords.latitude,
                longitude: pos.coords.longitude,
                accuracy: pos.coords.accuracy,
                timestamp: pos.timestamp,
              });
            },
            (err) => {
              console.error('Browser geolocation error:', err);
              reject({
                code: err.code,
                message: err.message || 'Unable to retrieve your location',
              });
            },
            {
              enableHighAccuracy: true,
              timeout: 8000,  // Shorter timeout than our overall timeout
              maximumAge: 5000 // Use a shorter maximumAge for more current data
            }
          );
        });
      }

      // Clear the timeout since we got a position
      clearTimeout(timeoutId);
      
      setPosition(position);

      try {
        // Get a formatted address from the backend
        const address = await invoke<string>('get_formatted_address', {
          latitude: position.latitude,
          longitude: position.longitude,
        });

        setFormattedAddress(address);
        
        // Send the location to the Rust backend
        await invoke('save_location', {
          location: {
            latitude: position.latitude,
            longitude: position.longitude,
            accuracy: position.accuracy,
            address: address,
          },
        });
      } catch (backendErr) {
        console.error('Error with backend location services:', backendErr);
        // Even if backend fails, we still have the position
        const fallbackAddress = `Location at ${position.latitude.toFixed(6)}, ${position.longitude.toFixed(6)}`;
        setFormattedAddress(fallbackAddress);
        
        // Try to save even with the fallback address
        try {
          await invoke('save_location', {
            location: {
              latitude: position.latitude,
              longitude: position.longitude,
              accuracy: position.accuracy,
              address: fallbackAddress,
            },
          });
        } catch (saveErr) {
          console.error('Failed to save location to backend:', saveErr);
        }
      }
    } catch (err: any) {
      console.error('Error getting location:', err);
      clearTimeout(timeoutId);
      
      // Provide more user-friendly error messages
      let errorMessage = 'Location unavailable';
      if (err.code === 1) {
        errorMessage = 'Location access denied. Please check your privacy settings.';
        // Update permission status if denied
        setPermissionStatus('denied');
      } else if (err.code === 2) {
        errorMessage = 'Location unavailable. Please check your device settings.';
      } else if (err.code === 3) {
        errorMessage = 'Location request timed out. Please try again.';
      }
      
      setError({
        code: err.code || 0,
        message: errorMessage
      });
      setFormattedAddress('Location unavailable');
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize on component mount
  useEffect(() => {
    // Request permission on component mount
    requestPermission();
    
    // We don't need to set up a watch position here since we're handling
    // position updates manually with getPosition
    
    // Clean up function
    return () => {
      // Clear any watchers or listeners if needed
      if (navigator.geolocation) {
        // Clear any active watchers - note that 0 is not a valid watch ID
        // We would need to store the actual watch ID if we were using watchPosition
      }
    };
  }, []);

  return {
    position,
    error,
    formattedAddress,
    isLoading,
    getPosition,
    permissionStatus,
    requestPermission
  };
}