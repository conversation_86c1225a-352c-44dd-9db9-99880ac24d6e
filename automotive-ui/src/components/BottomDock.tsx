import React from 'react';
import { motion } from 'framer-motion';
import {
  Navigation,
  Play,
  Thermometer,
  Settings,
  Car,
  ChevronUp
} from 'lucide-react';

export type ViewType = 'navigation' | 'media' | 'climate' | 'settings';

interface BottomDockProps {
  currentView: ViewType;
  onViewChange: (view: ViewType) => void;
  onToggleCarModel: () => void;
  showCarModel: boolean;
}

interface DockButtonProps {
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
  onClick: () => void;
  isPrimary?: boolean;
}

// Animation variants
const buttonVariants = {
  inactive: {
    scale: 1,
    y: 0,
    transition: { duration: 0.2, ease: "easeOut" }
  },
  active: {
    scale: 1.05,
    y: -2,
    transition: { duration: 0.3, ease: "easeOut" }
  },
  hover: {
    scale: 1.1,
    y: -4,
    transition: { duration: 0.2, ease: "easeOut" }
  },
  tap: {
    scale: 0.95,
    y: 0,
    transition: { duration: 0.1, ease: "easeOut" }
  }
};

const iconVariants = {
  inactive: {
    rotate: 0,
    transition: { duration: 0.2 }
  },
  active: {
    rotate: [0, -5, 5, 0],
    transition: { duration: 0.5, ease: "easeInOut" }
  },
  hover: {
    rotate: [0, 5, -5, 0],
    transition: { duration: 0.4, ease: "easeInOut" }
  }
};



const DockButton: React.FC<DockButtonProps> = ({ icon, label, isActive, onClick, isPrimary = false }) => {
  return (
    <motion.button
      onClick={onClick}
      variants={buttonVariants}
      initial="inactive"
      animate={isActive ? "active" : "inactive"}
      whileHover="hover"
      whileTap="tap"
      className={`relative flex flex-col items-center justify-center rounded-2xl group touch-target overflow-hidden ${
        isPrimary
          ? 'px-8 py-5 min-w-[140px]'
          : 'p-5 min-w-[90px]'
      } ${
        isActive
          ? isPrimary
            ? 'bg-primary text-white shadow-xl shadow-primary/25 hover-glow'
            : 'bg-neutral-900 text-white shadow-lg shadow-neutral-900/25'
          : 'text-neutral-600 hover:text-neutral-900 hover:bg-neutral-100'
      }`}
    >
      {/* Background glow effect for active state */}
      {isActive && (
        <motion.div
          className={`absolute inset-0 rounded-2xl ${
            isPrimary ? 'bg-primary' : 'bg-neutral-900'
          } opacity-10 blur-xl`}
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1.1, opacity: 0.1 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        />
      )}

      {/* Ripple effect on click */}
      <motion.div
        className="absolute inset-0 bg-white/20 rounded-2xl"
        initial={{ scale: 0, opacity: 0 }}
        whileTap={{ scale: 1, opacity: [0, 0.3, 0] }}
        transition={{ duration: 0.3 }}
      />

      <motion.div
        className={`relative ${
          isPrimary ? 'w-8 h-8 mb-3' : 'w-7 h-7 mb-2'
        }`}
        variants={iconVariants}
        initial="inactive"
        animate={isActive ? "active" : "inactive"}
        whileHover="hover"
      >
        {icon}
      </motion.div>

      <motion.span
        className={`relative font-semibold tracking-tight ${
          isPrimary ? 'text-sm' : 'text-xs'
        }`}
        initial={{ opacity: 0.8 }}
        animate={{ opacity: 1 }}
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
      >
        {label}
      </motion.span>

      {/* Active indicator */}
      {isActive && !isPrimary && (
        <motion.div
          className="absolute -bottom-2 w-8 h-1 bg-neutral-900 rounded-full"
          initial={{ scaleX: 0, opacity: 0 }}
          animate={{ scaleX: 1, opacity: 1 }}
          exit={{ scaleX: 0, opacity: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        />
      )}

      {/* Hover border effect */}
      <motion.div
        className="absolute inset-0 rounded-2xl border-2 border-neutral-200"
        initial={{ opacity: 0 }}
        whileHover={{ opacity: isActive ? 0 : 1 }}
        transition={{ duration: 0.2 }}
      />
    </motion.button>
  );
};

export const BottomDock: React.FC<BottomDockProps> = ({
  currentView,
  onViewChange,
  onToggleCarModel,
  showCarModel
}) => {
  const dockItems = [
    {
      id: 'navigation' as ViewType,
      icon: <Navigation className="w-full h-full" strokeWidth={2.5} />,
      label: 'Navigate'
    },
    {
      id: 'media' as ViewType,
      icon: <Play className="w-full h-full" strokeWidth={2.5} />,
      label: 'Media'
    },
    {
      id: 'climate' as ViewType,
      icon: <Thermometer className="w-full h-full" strokeWidth={2.5} />,
      label: 'Climate'
    },
    {
      id: 'settings' as ViewType,
      icon: <Settings className="w-full h-full" strokeWidth={2.5} />,
      label: 'Settings'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.8 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      className="flex items-center justify-between w-full max-w-5xl mx-auto"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Main Navigation Buttons */}
      <motion.div
        className="flex items-center space-x-4"
        variants={containerVariants}
      >
        {dockItems.map((item, index) => (
          <motion.div
            key={item.id}
            variants={itemVariants}
            whileHover={{ y: -2 }}
            transition={{ duration: 0.2 }}
          >
            <DockButton
              icon={item.icon}
              label={item.label}
              isActive={currentView === item.id && !showCarModel}
              onClick={() => onViewChange(item.id)}
            />
          </motion.div>
        ))}
      </motion.div>

      {/* Right side - Car Model Toggle */}
      <motion.div
        variants={itemVariants}
        whileHover={{ y: -2 }}
        transition={{ duration: 0.2 }}
      >
        <DockButton
          icon={
            <div className="relative">
              <Car className="w-full h-full" strokeWidth={2.5} />
              {showCarModel && (
                <motion.div
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.3, type: "spring", stiffness: 200 }}
                >
                  <ChevronUp className="absolute -top-1 -right-1 w-4 h-4 text-white bg-primary rounded-full p-0.5" strokeWidth={3} />
                </motion.div>
              )}
            </div>
          }
          label="Vehicle"
          isActive={showCarModel}
          onClick={onToggleCarModel}
          isPrimary={true}
        />
      </motion.div>
    </motion.div>
  );
}; 