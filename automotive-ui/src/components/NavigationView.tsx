import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Search,
  MapPin,
  Navigation as NavigationIcon,
  Map,
  Compass,
  Clock,
  Fuel,
  DollarSign,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { useGeolocation } from '../hooks/useGeolocation';


// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
};

const cardVariants = {
  hidden: { opacity: 0, scale: 0.95, y: 20 },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  },
  hover: {
    scale: 1.02,
    y: -5,
    transition: {
      duration: 0.2,
      ease: "easeOut"
    }
  }
};

export const NavigationView: React.FC = () => {
  const [destination, setDestination] = useState('');
  const { position, error, formattedAddress, isLoading, getPosition, permissionStatus, requestPermission } = useGeolocation();

  return (
    <motion.div
      className="h-full flex flex-col bg-neutral-50"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header Section */}
      <div className="p-8 space-y-8 flex-shrink-0">
        {/* Search input */}
        <motion.div
          className="space-y-6"
          variants={itemVariants}
        >
          <motion.h2
            className="text-4xl font-bold text-neutral-900 tracking-tight"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            Where to?
          </motion.h2>
          <motion.div
            className="relative"
            whileHover={{ scale: 1.01 }}
            whileFocus={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 w-7 h-7 text-neutral-400" strokeWidth={2} />
            <motion.input
              type="text"
              value={destination}
              onChange={(e) => setDestination(e.target.value)}
              placeholder="Search destination or address"
              className="input-large w-full pl-20 pr-6 transition-all duration-300"
              whileFocus={{
                boxShadow: "0 0 0 4px rgba(37, 99, 235, 0.1)",
                borderColor: "rgb(37, 99, 235)"
              }}
            />
          </motion.div>
        </motion.div>

        {/* Current location */}
        <motion.div
          className="card-glass p-8"
          variants={cardVariants}
          whileHover="hover"
        >
          <div className="flex items-start space-x-6">
            <motion.div
              className="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-sm"
              whileHover={{
                scale: 1.1,
                rotate: [0, -5, 5, 0],
                transition: { duration: 0.3 }
              }}
            >
              <MapPin className="w-8 h-8 text-primary" strokeWidth={2.5} />
            </motion.div>
            <div className="flex-1 min-w-0">
              <div className="flex justify-between items-center mb-3">
                <motion.h3
                  className="text-2xl font-bold text-neutral-900"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.4 }}
                >
                  Current Location
                </motion.h3>
                
                <motion.button
                  className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center"
                  whileHover={{ scale: 1.1, backgroundColor: 'rgba(59, 130, 246, 0.2)' }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.4 }}
                  onClick={requestPermission}
                >
                  <RefreshCw
                    className={`w-5 h-5 text-primary ${isLoading ? 'animate-spin' : ''}`}
                    strokeWidth={2}
                  />
                </motion.button>
              </div>
              
              <motion.p
                className="text-lg text-neutral-700 break-words leading-relaxed mb-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4, duration: 0.4 }}
              >
                {formattedAddress}
              </motion.p>
              {isLoading ? (
                <motion.div
                  className="flex items-center text-primary bg-primary/10 px-4 py-2 rounded-xl w-fit"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5, duration: 0.3, type: "spring", stiffness: 200 }}
                >
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  >
                    <Compass className="w-5 h-5 mr-3" strokeWidth={2.5} />
                  </motion.div>
                  <span className="font-semibold">Acquiring GPS Signal...</span>
                </motion.div>
              ) : error ? (
                <motion.div
                  className="flex items-center text-red-500 bg-red-500/10 px-4 py-2 rounded-xl w-fit"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5, duration: 0.3, type: "spring", stiffness: 200 }}
                >
                  <AlertCircle className="w-5 h-5 mr-3" strokeWidth={2.5} />
                  <span className="font-semibold">GPS Error • {error.message}</span>
                </motion.div>
              ) : position ? (
                <motion.div
                  className="flex items-center text-success bg-success/10 px-4 py-2 rounded-xl w-fit"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5, duration: 0.3, type: "spring", stiffness: 200 }}
                >
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  >
                    <Compass className="w-5 h-5 mr-3" strokeWidth={2.5} />
                  </motion.div>
                  <span className="font-semibold">
                    GPS Connected • Signal {position.accuracy < 20 ? 'Strong' : position.accuracy < 50 ? 'Moderate' : 'Weak'}
                  </span>
                </motion.div>
              ) : (
                <motion.div
                  className="flex items-center text-yellow-500 bg-yellow-500/10 px-4 py-2 rounded-xl w-fit"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5, duration: 0.3, type: "spring", stiffness: 200 }}
                >
                  <Compass className="w-5 h-5 mr-3" strokeWidth={2.5} />
                  <span className="font-semibold">GPS Status Unknown</span>
                </motion.div>
              )}
            </div>
          </div>
        </motion.div>

        {/* Trip Summary */}
        <motion.div
          className="card p-8"
          variants={cardVariants}
          whileHover="hover"
        >
          <div className="flex items-center justify-between mb-8">
            <motion.h3
              className="text-2xl font-bold text-neutral-900"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6, duration: 0.4 }}
            >
              Trip Summary
            </motion.h3>
            <motion.div
              className="w-14 h-14 bg-success/10 rounded-2xl flex items-center justify-center"
              whileHover={{
                scale: 1.1,
                rotate: 15,
                transition: { duration: 0.2 }
              }}
              whileTap={{ scale: 0.95 }}
            >
              <NavigationIcon className="w-7 h-7 text-success" strokeWidth={2.5} />
            </motion.div>
          </div>

          <motion.div
            className="grid grid-cols-3 gap-6"
            variants={containerVariants}
          >
            <motion.div
              className="text-center p-6 bg-neutral-50 rounded-2xl"
              variants={itemVariants}
              whileHover={{
                scale: 1.05,
                y: -5,
                transition: { duration: 0.2 }
              }}
            >
              <motion.div
                className="text-3xl font-bold text-neutral-900 mb-2"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.8, duration: 0.3, type: "spring", stiffness: 200 }}
              >
                12.4
              </motion.div>
              <div className="text-neutral-600 font-semibold uppercase tracking-wide text-sm">Miles</div>
            </motion.div>

            <motion.div
              className="text-center p-6 bg-neutral-50 rounded-2xl"
              variants={itemVariants}
              whileHover={{
                scale: 1.05,
                y: -5,
                transition: { duration: 0.2 }
              }}
            >
              <div className="flex items-center justify-center mb-2">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  <Clock className="w-6 h-6 text-primary mr-2" strokeWidth={2.5} />
                </motion.div>
                <motion.span
                  className="text-3xl font-bold text-neutral-900"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.9, duration: 0.3, type: "spring", stiffness: 200 }}
                >
                  18
                </motion.span>
              </div>
              <div className="text-neutral-600 font-semibold uppercase tracking-wide text-sm">Minutes</div>
            </motion.div>

            <motion.div
              className="text-center p-6 bg-success/5 rounded-2xl border border-success/20"
              variants={itemVariants}
              whileHover={{
                scale: 1.05,
                y: -5,
                boxShadow: "0 10px 25px rgba(16, 185, 129, 0.15)",
                transition: { duration: 0.2 }
              }}
            >
              <div className="flex items-center justify-center mb-2">
                <motion.div
                  animate={{ y: [0, -2, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                >
                  <DollarSign className="w-6 h-6 text-success mr-1" strokeWidth={2.5} />
                </motion.div>
                <motion.span
                  className="text-3xl font-bold text-success"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 1.0, duration: 0.3, type: "spring", stiffness: 200 }}
                >
                  3.20
                </motion.span>
              </div>
              <div className="text-neutral-600 font-semibold uppercase tracking-wide text-sm">Fuel Cost</div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>

      {/* Map Area */}
      <motion.div
        className="flex-1 relative bg-gradient-to-br from-primary/5 via-white to-neutral-50 m-8 mt-0 rounded-3xl overflow-hidden border-2 border-neutral-200 shadow-xl"
        variants={itemVariants}
        whileHover={{ scale: 1.01 }}
        transition={{ duration: 0.3 }}
      >
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center max-w-lg px-8">
            <motion.div
              className="w-32 h-32 bg-gradient-to-br from-primary/10 to-primary/20 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-xl"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: 1.2, duration: 0.8, type: "spring", stiffness: 100 }}
              whileHover={{
                scale: 1.1,
                rotate: [0, 5, -5, 0],
                transition: { duration: 0.5 }
              }}
            >
              <Map className="w-16 h-16 text-primary" strokeWidth={2} />
            </motion.div>

            <motion.h3
              className="text-3xl font-bold text-neutral-900 mb-6 tracking-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.4, duration: 0.5 }}
            >
              Navigation Ready
            </motion.h3>

            <motion.p
              className="text-xl text-neutral-600 mb-10 leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.6, duration: 0.5 }}
            >
              High-precision GPS navigation with real-time traffic updates and intelligent route optimization.
            </motion.p>

            {isLoading ? (
              <motion.div
                className="flex items-center justify-center space-x-4 text-primary bg-primary/10 px-8 py-4 rounded-2xl border border-primary/20"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.8, duration: 0.4, type: "spring", stiffness: 200 }}
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 10px 25px rgba(37, 99, 235, 0.15)",
                  transition: { duration: 0.2 }
                }}
              >
                <motion.div
                  className="w-4 h-4 bg-primary rounded-full"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1, repeat: Infinity, ease: "easeInOut" }}
                />
                <span className="font-bold text-lg">Acquiring GPS Signal...</span>
              </motion.div>
            ) : error ? (
              <motion.div
                className="flex flex-col items-center justify-center space-y-3 text-red-500 bg-red-500/10 px-8 py-4 rounded-2xl border border-red-500/20"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.8, duration: 0.4, type: "spring", stiffness: 200 }}
                whileHover={{
                  scale: 1.02,
                  boxShadow: "0 10px 25px rgba(239, 68, 68, 0.15)",
                  transition: { duration: 0.2 }
                }}
              >
                <div className="flex items-center">
                  <AlertCircle className="w-5 h-5 mr-2" strokeWidth={2.5} />
                  <span className="font-bold text-lg">
                    GPS Error • {error?.message || 'Unknown error'}
                  </span>
                </div>
                
                <motion.button
                  className="bg-red-500 text-white px-6 py-3 rounded-xl font-semibold shadow-lg"
                  whileHover={{ scale: 1.05, backgroundColor: '#ef4444' }}
                  whileTap={{ scale: 0.95 }}
                  onClick={error?.code === 1 ? requestPermission : getPosition}
                >
                  {error?.code === 1 ? 'Grant Location Permission' : 'Retry'}
                </motion.button>
              </motion.div>
            ) : position ? (
              <motion.div
                className="flex items-center justify-center space-x-4 text-success bg-success/10 px-8 py-4 rounded-2xl border border-success/20"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.8, duration: 0.4, type: "spring", stiffness: 200 }}
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 10px 25px rgba(16, 185, 129, 0.15)",
                  transition: { duration: 0.2 }
                }}
              >
                <motion.div
                  className="w-4 h-4 bg-success rounded-full"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                />
                <span className="font-bold text-lg">
                  GPS Signal {position.accuracy < 20 ? 'Strong' : position.accuracy < 50 ? 'Moderate' : 'Weak'}
                </span>
              </motion.div>
            ) : (
              <motion.div
                className="flex flex-col items-center justify-center space-y-3 text-yellow-500 bg-yellow-500/10 px-8 py-4 rounded-2xl border border-yellow-500/20"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.8, duration: 0.4, type: "spring", stiffness: 200 }}
                whileHover={{
                  scale: 1.02,
                  boxShadow: "0 10px 25px rgba(245, 158, 11, 0.15)",
                  transition: { duration: 0.2 }
                }}
              >
                <div className="flex items-center">
                  <Compass className="w-5 h-5 mr-2" strokeWidth={2.5} />
                  <span className="font-bold text-lg">Location Services Required</span>
                </div>
                
                <p className="text-center text-neutral-700 mb-2">
                  Enable location access to use navigation features
                </p>
                
                <motion.button
                  className="bg-yellow-500 text-white px-6 py-3 rounded-xl font-semibold shadow-lg"
                  whileHover={{ scale: 1.05, backgroundColor: '#f59e0b' }}
                  whileTap={{ scale: 0.95 }}
                  onClick={requestPermission}
                >
                  Enable Location
                </motion.button>
              </motion.div>
            )}
          </div>
        </div>

        {/* Floating Action Button */}
        <motion.div
          className="absolute bottom-8 right-8"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 2.0, duration: 0.4, type: "spring", stiffness: 200 }}
        >
          <motion.button
            className="btn-primary w-18 h-18 rounded-2xl shadow-2xl flex items-center justify-center group"
            whileHover={{
              scale: 1.1,
              boxShadow: "0 20px 40px rgba(37, 99, 235, 0.3)",
              transition: { duration: 0.2 }
            }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              whileHover={{ rotate: 15 }}
              transition={{ duration: 0.2 }}
            >
              <NavigationIcon className="w-9 h-9" strokeWidth={2.5} />
            </motion.div>
          </motion.button>
        </motion.div>

        {/* Status Pills */}
        <motion.div
          className="absolute top-6 left-6 flex space-x-4"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 2.2, duration: 0.5 }}
        >
          <motion.div
            className="glass px-6 py-3 rounded-2xl shadow-lg"
            whileHover={{
              scale: 1.05,
              y: -2,
              transition: { duration: 0.2 }
            }}
          >
            <div className="flex items-center space-x-3 text-sm font-semibold text-neutral-700">
              <motion.div
                className="w-3 h-3 bg-primary rounded-full"
                animate={{ opacity: [1, 0.3, 1] }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
              />
              <span>Live Traffic</span>
            </div>
          </motion.div>

          <motion.div
            className={`glass px-6 py-3 rounded-2xl shadow-lg ${position ? 'bg-success/10' : error ? 'bg-red-500/10' : 'bg-yellow-500/10'}`}
            whileHover={{
              scale: 1.05,
              y: -2,
              transition: { duration: 0.2 }
            }}
            onClick={requestPermission}
          >
            <div className="flex items-center space-x-3 text-sm font-semibold text-neutral-700">
              <motion.div
                className={`w-3 h-3 rounded-full ${position ? 'bg-success' : error ? 'bg-red-500' : 'bg-yellow-500'}`}
                animate={{ opacity: [1, 0.3, 1] }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
              />
              <span>
                {position ? 'GPS Active' :
                 permissionStatus === 'denied' ? 'GPS Denied' :
                 permissionStatus === 'granted' ? 'GPS Granted' :
                 error ? 'GPS Error' : 'GPS Inactive'}
              </span>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}; 