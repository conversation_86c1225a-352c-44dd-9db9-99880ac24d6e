import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Battery,
  Gauge,
  Fuel,
  Thermometer,
  Zap,
  Clock
} from 'lucide-react';

interface VehicleStatus {
  starterBattery: number; // Voltage
  auxBattery: number; // Voltage
  speed: number;
  fuelLevel: number; // Percentage
  range: number;
  engineTemp: number;
  isRunning: boolean;
}

// Enhanced Battery Component with better visual feedback and animations
const BatteryVoltageIcon: React.FC<{ voltage: number; type: 'starter' | 'aux' }> = ({ voltage, type }) => {
  const getStatus = () => {
    if (type === 'starter') {
      if (voltage >= 12.4) return { color: 'text-success', bgColor: 'bg-success', level: 'good', severity: 'good' };
      if (voltage >= 11.8) return { color: 'text-warning', bgColor: 'bg-warning', level: 'warning', severity: 'warning' };
      return { color: 'text-error', bgColor: 'bg-error', level: 'critical', severity: 'critical' };
    } else {
      if (voltage >= 12.0) return { color: 'text-success', bgColor: 'bg-success', level: 'good', severity: 'good' };
      if (voltage >= 11.5) return { color: 'text-warning', bgColor: 'bg-warning', level: 'warning', severity: 'warning' };
      return { color: 'text-error', bgColor: 'bg-error', level: 'critical', severity: 'critical' };
    }
  };

  const status = getStatus();
  const batteryLevel = Math.min(100, Math.max(0, ((voltage - 10) / 4) * 100));

  return (
    <motion.div
      className="flex items-center space-x-3"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <motion.div
        className="relative"
        whileHover={{ scale: 1.1 }}
        transition={{ duration: 0.2 }}
      >
        <motion.div
          animate={status.severity === 'critical' ? {
            scale: [1, 1.1, 1],
            rotate: [0, -2, 2, 0]
          } : {}}
          transition={{
            duration: 1.5,
            repeat: status.severity === 'critical' ? Infinity : 0,
            ease: "easeInOut"
          }}
        >
          <Battery className={`w-6 h-6 ${status.color} transition-colors duration-500`} />
        </motion.div>

        <motion.div
          className={`absolute inset-0 ${status.bgColor} opacity-20 rounded`}
          initial={{ clipPath: 'inset(100% 0 0 0)' }}
          animate={{ clipPath: `inset(${100 - batteryLevel}% 0 0 0)` }}
          transition={{ duration: 1, ease: "easeOut" }}
        />

        {/* Charging animation for good status */}
        {status.severity === 'good' && (
          <motion.div
            className="absolute inset-0 bg-success/30 rounded"
            animate={{ opacity: [0, 0.3, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            style={{ clipPath: `inset(${100 - batteryLevel}% 0 0 0)` }}
          />
        )}
      </motion.div>

      <div className="flex flex-col">
        <motion.span
          className={`text-sm font-semibold ${status.color} transition-colors duration-500`}
          key={voltage}
          initial={{ scale: 1.2, opacity: 0.7 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          {voltage.toFixed(1)}V
        </motion.span>
        <motion.span
          className="text-xs text-neutral-400 uppercase tracking-wide"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.3 }}
        >
          {status.level}
        </motion.span>
      </div>
    </motion.div>
  );
};

// Modern Turbo Logo with enhanced styling and animations
const TurboLogo = ({ className }: { className?: string }) => (
  <motion.div
    className="relative"
    whileHover={{ scale: 1.1 }}
    transition={{ duration: 0.2 }}
  >
    <motion.div
      animate={{
        rotate: [0, 5, -5, 0],
        scale: [1, 1.05, 1]
      }}
      transition={{
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    >
      <Zap className={`${className} transition-all duration-300`} />
    </motion.div>
    <motion.div
      className="absolute inset-0 bg-gradient-to-r from-primary to-primary-light opacity-20 rounded-full blur-sm"
      animate={{ opacity: [0.2, 0.4, 0.2] }}
      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
    />
  </motion.div>
);

export const VehicleStatusBar: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [vehicleStatus, setVehicleStatus] = useState<VehicleStatus>({
    starterBattery: 12.6,
    auxBattery: 12.2,
    speed: 0,
    fuelLevel: 68,
    range: 287,
    engineTemp: 185,
    isRunning: true
  });

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Simulate vehicle data updates
  useEffect(() => {
    const dataTimer = setInterval(() => {
      setVehicleStatus(prev => ({
        ...prev,
        speed: Math.max(0, prev.speed + (Math.random() - 0.5) * 5),
        starterBattery: Math.max(11.0, Math.min(14.4, prev.starterBattery + (Math.random() - 0.5) * 0.02)),
        auxBattery: Math.max(10.5, Math.min(13.8, prev.auxBattery + (Math.random() - 0.5) * 0.02)),
        fuelLevel: Math.max(0, Math.min(100, prev.fuelLevel + (Math.random() - 0.5) * 0.1)),
        range: Math.max(0, prev.range + (Math.random() - 0.5) * 2),
        engineTemp: Math.max(160, Math.min(220, prev.engineTemp + (Math.random() - 0.5) * 1))
      }));
    }, 3000);
    return () => clearInterval(dataTimer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const containerVariants = {
    hidden: { opacity: 0, y: -50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      className="bg-neutral-950 text-white px-8 py-5 flex items-center justify-between backdrop-blur-xl border-b border-neutral-800/50"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Left side - Vehicle stats */}
      <motion.div
        className="flex items-center space-x-10"
        variants={containerVariants}
      >
        {/* Starter Battery */}
        <motion.div
          className="flex flex-col space-y-1"
          variants={itemVariants}
        >
          <motion.div
            className="text-xs text-neutral-400 uppercase tracking-wider font-medium"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.3 }}
          >
            Starter
          </motion.div>
          <BatteryVoltageIcon voltage={vehicleStatus.starterBattery} type="starter" />
        </motion.div>

        {/* Auxiliary Battery */}
        <motion.div
          className="flex flex-col space-y-1"
          variants={itemVariants}
        >
          <motion.div
            className="text-xs text-neutral-400 uppercase tracking-wider font-medium"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.3 }}
          >
            Auxiliary
          </motion.div>
          <BatteryVoltageIcon voltage={vehicleStatus.auxBattery} type="aux" />
        </motion.div>

        {/* Speed */}
        <motion.div
          className="flex items-center space-x-4"
          variants={itemVariants}
        >
          <motion.div
            className="p-2 bg-primary/10 rounded-xl"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.2 }}
          >
            <motion.div
              animate={{ rotate: vehicleStatus.speed > 0 ? [0, 360] : 0 }}
              transition={{
                duration: 2,
                repeat: vehicleStatus.speed > 0 ? Infinity : 0,
                ease: "linear"
              }}
            >
              <Gauge className="w-6 h-6 text-primary transition-colors duration-300" />
            </motion.div>
          </motion.div>
          <div className="flex flex-col">
            <div className="flex items-baseline space-x-1">
              <motion.span
                className="text-3xl font-light tracking-tight"
                key={Math.round(vehicleStatus.speed)}
                initial={{ scale: 1.2, opacity: 0.7 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              >
                {Math.round(vehicleStatus.speed)}
              </motion.span>
              <span className="text-sm text-neutral-400 font-medium">mph</span>
            </div>
            <span className="text-xs text-neutral-500 uppercase tracking-wide">Speed</span>
          </div>
        </motion.div>

        {/* Fuel */}
        <motion.div
          className="flex items-center space-x-4"
          variants={itemVariants}
        >
          <motion.div
            className="p-2 bg-warning/10 rounded-xl"
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.2 }}
          >
            <motion.div
              animate={vehicleStatus.fuelLevel < 20 ? {
                y: [0, -2, 0],
                scale: [1, 1.1, 1]
              } : {}}
              transition={{
                duration: 1,
                repeat: vehicleStatus.fuelLevel < 20 ? Infinity : 0,
                ease: "easeInOut"
              }}
            >
              <Fuel className="w-6 h-6 text-warning transition-colors duration-300" />
            </motion.div>
          </motion.div>
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <motion.span
                className="text-lg font-semibold"
                key={Math.round(vehicleStatus.fuelLevel)}
                initial={{ scale: 1.2, opacity: 0.7 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              >
                {Math.round(vehicleStatus.fuelLevel)}%
              </motion.span>
              <motion.span
                className="text-sm text-neutral-400"
                key={Math.round(vehicleStatus.range)}
                initial={{ opacity: 0.5 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                • {Math.round(vehicleStatus.range)} mi
              </motion.span>
            </div>
            <span className="text-xs text-neutral-500 uppercase tracking-wide">Fuel & Range</span>
          </div>
        </motion.div>

        {/* Engine Temp */}
        <motion.div
          className="flex items-center space-x-4"
          variants={itemVariants}
        >
          <motion.div
            className="p-2 bg-success/10 rounded-xl"
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.2 }}
          >
            <motion.div
              animate={vehicleStatus.engineTemp > 200 ? {
                rotate: [0, -5, 5, 0],
                scale: [1, 1.1, 1]
              } : {}}
              transition={{
                duration: 1.5,
                repeat: vehicleStatus.engineTemp > 200 ? Infinity : 0,
                ease: "easeInOut"
              }}
            >
              <Thermometer className="w-6 h-6 text-success transition-colors duration-300" />
            </motion.div>
          </motion.div>
          <div className="flex flex-col">
            <motion.span
              className="text-lg font-semibold"
              key={Math.round(vehicleStatus.engineTemp)}
              initial={{ scale: 1.2, opacity: 0.7 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              {Math.round(vehicleStatus.engineTemp)}°F
            </motion.span>
            <span className="text-xs text-neutral-500 uppercase tracking-wide">Engine Temp</span>
          </div>
        </motion.div>
      </motion.div>

      {/* Center - Turbo logo */}
      <motion.div
        className="flex items-center space-x-4 px-6 py-3 bg-white/5 rounded-2xl backdrop-blur-sm border border-white/10"
        variants={itemVariants}
        whileHover={{
          scale: 1.05,
          boxShadow: "0 0 20px rgba(255, 255, 255, 0.1)",
          transition: { duration: 0.2 }
        }}
      >
        <motion.div
          className="w-10 h-10 bg-gradient-to-br from-primary to-primary-light rounded-xl flex items-center justify-center shadow-lg"
          whileHover={{ rotate: 360 }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
        >
          <TurboLogo className="w-7 h-7 text-white" />
        </motion.div>
        <motion.div
          className="text-xl font-bold tracking-wider bg-gradient-to-r from-white to-neutral-300 bg-clip-text text-transparent"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.8, duration: 0.4 }}
        >
          TURBO
        </motion.div>
      </motion.div>

      {/* Right side - Time and date */}
      <motion.div
        className="text-right space-y-1"
        variants={itemVariants}
      >
        <motion.div
          className="flex items-center space-x-2 justify-end"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 60, repeat: Infinity, ease: "linear" }}
          >
            <Clock className="w-5 h-5 text-neutral-400" />
          </motion.div>
          <motion.div
            className="text-2xl font-light tracking-tight"
            key={formatTime(currentTime)}
            initial={{ opacity: 0.7, scale: 1.05 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            {formatTime(currentTime)}
          </motion.div>
        </motion.div>
        <motion.div
          className="text-sm text-neutral-400 font-medium"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.0, duration: 0.3 }}
        >
          {formatDate(currentTime)}
        </motion.div>
      </motion.div>
    </motion.div>
  );
}; 