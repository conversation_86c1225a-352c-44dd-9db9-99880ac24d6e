// Learn more about <PERSON>ri commands at https://tauri.app/develop/calling-rust/
use serde::{Deserialize, Serialize};
use std::sync::Mutex;
use headless_chrome::{<PERSON>rowser, LaunchOptions};
use std::time::Duration;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct GpsLocation {
    latitude: f64,
    longitude: f64,
    accuracy: Option<f64>,
    address: Option<String>,
}

// Store the last known location in memory
struct AppState {
    last_location: Mutex<Option<GpsLocation>>,
}

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
fn save_location(location: GpsLocation, state: tauri::State<AppState>) -> Result<String, String> {
    println!("Received location: {:?}", location);
    
    // Store the location in our app state
    let mut last_location = state.last_location.lock().unwrap();
    *last_location = Some(location.clone());
    
    // In a real application, you might store this in a database or use it for navigation
    Ok("Location received successfully".to_string())
}

#[tauri::command]
fn get_formatted_address(latitude: f64, longitude: f64) -> Result<String, String> {
    // In a production app, you would use a geocoding service here
    // For now, we'll just return the coordinates as a string
    Ok(format!("Location at {:.6}, {:.6}", latitude, longitude))
}

#[tauri::command]
fn get_last_location(state: tauri::State<AppState>) -> Result<Option<GpsLocation>, String> {
    let last_location = state.last_location.lock().unwrap();
    Ok(last_location.clone())
}

#[tauri::command]
fn show_location_permission_dialog() -> Result<String, String> {
    // Return a message that the frontend can display
    Ok("This application needs access to your location to provide navigation features. Please allow location access when prompted by your browser or system.".to_string())
}

#[tauri::command]
async fn get_location_headless(state: tauri::State<'_, AppState>) -> Result<GpsLocation, String> {
    println!("Starting headless browser geolocation...");

    // Launch headless Chrome with geolocation permissions
    let browser = Browser::new(LaunchOptions {
        headless: true,
        sandbox: false,
        ..Default::default()
    }).map_err(|e| format!("Failed to launch browser: {}", e))?;

    let tab = browser.new_tab().map_err(|e| format!("Failed to create tab: {}", e))?;

    // Navigate to a geolocation service that works without user permission
    // Using ipapi.co which provides location based on IP address
    tab.navigate_to("https://ipapi.co/json/")
        .map_err(|e| format!("Failed to navigate to geolocation service: {}", e))?;

    // Wait for page to load
    tab.wait_until_navigated()
        .map_err(|e| format!("Failed to wait for navigation: {}", e))?;

    // Wait a bit for the JSON to load
    tokio::time::sleep(Duration::from_secs(2)).await;

    // Get the page content
    let content = tab.get_content()
        .map_err(|e| format!("Failed to get page content: {}", e))?;

    // Parse the JSON response
    let json_data: serde_json::Value = serde_json::from_str(&content)
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    // Extract latitude and longitude
    let latitude = json_data.get("latitude")
        .and_then(|v| v.as_f64())
        .ok_or("Failed to get latitude from response")?;

    let longitude = json_data.get("longitude")
        .and_then(|v| v.as_f64())
        .ok_or("Failed to get longitude from response")?;

    // Get city and region for address
    let city = json_data.get("city").and_then(|v| v.as_str()).unwrap_or("");
    let region = json_data.get("region").and_then(|v| v.as_str()).unwrap_or("");
    let country = json_data.get("country_name").and_then(|v| v.as_str()).unwrap_or("");

    let address = if !city.is_empty() && !region.is_empty() {
        format!("{}, {}, {}", city, region, country)
    } else {
        format!("Location at {:.6}, {:.6}", latitude, longitude)
    };

    let location = GpsLocation {
        latitude,
        longitude,
        accuracy: Some(1000.0), // IP-based location has lower accuracy
        address: Some(address),
    };

    // Store the location
    let mut last_location = state.last_location.lock().unwrap();
    *last_location = Some(location.clone());

    println!("Headless browser geolocation successful: {:?}", location);
    Ok(location)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let app_state = AppState {
        last_location: Mutex::new(None),
    };

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        // Note: tauri_plugin_geolocation removed - primarily for mobile platforms
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            greet,
            save_location,
            get_formatted_address,
            get_last_location,
            show_location_permission_dialog,
            get_location_headless
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
