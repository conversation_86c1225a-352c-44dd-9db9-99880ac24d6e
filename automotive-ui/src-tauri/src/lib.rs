// Learn more about <PERSON>ri commands at https://tauri.app/develop/calling-rust/
use serde::{Deserialize, Serialize};
use std::sync::Mutex;
use headless_chrome::{<PERSON>rowser, LaunchOptions};
use std::time::Duration;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct GpsLocation {
    latitude: f64,
    longitude: f64,
    accuracy: Option<f64>,
    address: Option<String>,
}

// Store the last known location in memory
struct AppState {
    last_location: Mutex<Option<GpsLocation>>,
}

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
fn save_location(location: GpsLocation, state: tauri::State<AppState>) -> Result<String, String> {
    println!("Received location: {:?}", location);
    
    // Store the location in our app state
    let mut last_location = state.last_location.lock().unwrap();
    *last_location = Some(location.clone());
    
    // In a real application, you might store this in a database or use it for navigation
    Ok("Location received successfully".to_string())
}

#[tauri::command]
fn get_formatted_address(latitude: f64, longitude: f64) -> Result<String, String> {
    // In a production app, you would use a geocoding service here
    // For now, we'll just return the coordinates as a string
    Ok(format!("Location at {:.6}, {:.6}", latitude, longitude))
}

#[tauri::command]
fn get_last_location(state: tauri::State<AppState>) -> Result<Option<GpsLocation>, String> {
    let last_location = state.last_location.lock().unwrap();
    Ok(last_location.clone())
}

#[tauri::command]
fn show_location_permission_dialog() -> Result<String, String> {
    // Return a message that the frontend can display
    Ok("This application needs access to your location to provide navigation features. Please allow location access when prompted by your browser or system.".to_string())
}

#[tauri::command]
async fn get_location_headless(state: tauri::State<'_, AppState>) -> Result<GpsLocation, String> {
    println!("🚀 Starting headless browser geolocation...");

    // Launch headless Chrome with geolocation permissions
    let browser = Browser::new(LaunchOptions {
        headless: true,
        sandbox: false,
        ..Default::default()
    }).map_err(|e| {
        println!("❌ Failed to launch browser: {}", e);
        format!("Failed to launch browser: {}", e)
    })?;

    println!("✅ Browser launched successfully");
    let tab = browser.new_tab().map_err(|e| {
        println!("❌ Failed to create tab: {}", e);
        format!("Failed to create tab: {}", e)
    })?;

    println!("✅ Tab created successfully");

    // Create a simple HTML page that uses navigator.geolocation (same as localhost:1420)
    let html_content = r#"
    <!DOCTYPE html>
    <html>
    <head>
        <title>Geolocation</title>
        <meta charset="utf-8">
    </head>
    <body>
        <div id="result">Loading...</div>
        <script>
            console.log('🔍 Starting geolocation request...');

            function getLocation() {
                return new Promise((resolve, reject) => {
                    if (!navigator.geolocation) {
                        console.error('❌ Geolocation not supported');
                        reject(new Error('Geolocation not supported'));
                        return;
                    }

                    console.log('📍 Requesting current position...');
                    navigator.geolocation.getCurrentPosition(
                        (position) => {
                            console.log('✅ Geolocation success:', position);
                            const result = {
                                latitude: position.coords.latitude,
                                longitude: position.coords.longitude,
                                accuracy: position.coords.accuracy,
                                timestamp: position.timestamp
                            };

                            // Store result in window for Rust to access
                            window.locationResult = result;
                            document.getElementById('result').textContent = JSON.stringify(result);
                            resolve(result);
                        },
                        (error) => {
                            console.error('❌ Geolocation error:', error);
                            const errorResult = {
                                error: true,
                                code: error.code,
                                message: error.message
                            };
                            window.locationResult = errorResult;
                            document.getElementById('result').textContent = JSON.stringify(errorResult);
                            reject(error);
                        },
                        {
                            enableHighAccuracy: true,
                            timeout: 15000,
                            maximumAge: 0
                        }
                    );
                });
            }

            // Start the geolocation request immediately
            getLocation().catch(console.error);
        </script>
    </body>
    </html>
    "#;

    // Navigate to data URL with our HTML
    let data_url = format!("data:text/html,{}", urlencoding::encode(html_content));
    println!("📄 Navigating to geolocation page...");

    tab.navigate_to(&data_url).map_err(|e| {
        println!("❌ Failed to navigate: {}", e);
        format!("Failed to navigate: {}", e)
    })?;

    // Wait for page to load
    tab.wait_until_navigated().map_err(|e| {
        println!("❌ Failed to wait for navigation: {}", e);
        format!("Failed to wait for navigation: {}", e)
    })?;

    println!("✅ Page loaded, waiting for geolocation result...");

    // Wait for the geolocation to complete (up to 20 seconds)
    let mut attempts = 0;
    let max_attempts = 40; // 20 seconds with 500ms intervals

    loop {
        tokio::time::sleep(Duration::from_millis(500)).await;
        attempts += 1;

        // Check if we have a result
        let result = tab.evaluate("window.locationResult", false)
            .map_err(|e| format!("Failed to evaluate result: {}", e))?;

        if let Some(value) = result.value {
            println!("📊 Got result from browser: {:?}", value);

            // Check if it's an error
            if let Some(error_flag) = value.get("error") {
                if error_flag.as_bool() == Some(true) {
                    let error_msg = value.get("message")
                        .and_then(|v| v.as_str())
                        .unwrap_or("Unknown geolocation error");
                    println!("❌ Browser geolocation error: {}", error_msg);
                    return Err(format!("Browser geolocation error: {}", error_msg));
                }
            }

            // Extract location data
            if let (Some(lat), Some(lng)) = (
                value.get("latitude").and_then(|v| v.as_f64()),
                value.get("longitude").and_then(|v| v.as_f64())
            ) {
                let accuracy = value.get("accuracy").and_then(|v| v.as_f64());

                let location = GpsLocation {
                    latitude: lat,
                    longitude: lng,
                    accuracy,
                    address: None, // We'll get this separately if needed
                };

                // Store the location
                let mut last_location = state.last_location.lock().unwrap();
                *last_location = Some(location.clone());

                println!("✅ Headless browser geolocation successful: {:?}", location);
                return Ok(location);
            }
        }

        if attempts >= max_attempts {
            println!("⏰ Geolocation request timed out after {} attempts", attempts);
            return Err("Geolocation request timed out".to_string());
        }

        if attempts % 10 == 0 {
            println!("⏳ Still waiting for geolocation... (attempt {}/{})", attempts, max_attempts);
        }
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let app_state = AppState {
        last_location: Mutex::new(None),
    };

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        // Note: tauri_plugin_geolocation removed - primarily for mobile platforms
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            greet,
            save_location,
            get_formatted_address,
            get_last_location,
            show_location_permission_dialog,
            get_location_headless
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
