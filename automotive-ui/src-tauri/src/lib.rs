// Learn more about <PERSON>ri commands at https://tauri.app/develop/calling-rust/
use serde::{Deserialize, Serialize};
use std::sync::Mutex;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct GpsLocation {
    latitude: f64,
    longitude: f64,
    accuracy: Option<f64>,
    address: Option<String>,
}

// Store the last known location in memory
struct AppState {
    last_location: Mutex<Option<GpsLocation>>,
}

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
fn save_location(location: GpsLocation, state: tauri::State<AppState>) -> Result<String, String> {
    println!("Received location: {:?}", location);
    
    // Store the location in our app state
    let mut last_location = state.last_location.lock().unwrap();
    *last_location = Some(location.clone());
    
    // In a real application, you might store this in a database or use it for navigation
    Ok("Location received successfully".to_string())
}

#[tauri::command]
fn get_formatted_address(latitude: f64, longitude: f64) -> Result<String, String> {
    // In a production app, you would use a geocoding service here
    // For now, we'll just return the coordinates as a string
    Ok(format!("Location at {:.6}, {:.6}", latitude, longitude))
}

#[tauri::command]
fn get_last_location(state: tauri::State<AppState>) -> Result<Option<GpsLocation>, String> {
    let last_location = state.last_location.lock().unwrap();
    Ok(last_location.clone())
}

#[tauri::command]
fn show_location_permission_dialog() -> Result<String, String> {
    // Return a message that the frontend can display
    Ok("This application needs access to your location to provide navigation features. Please allow location access when prompted by your browser or system.".to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let app_state = AppState {
        last_location: Mutex::new(None),
    };

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_geolocation::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            greet,
            save_location,
            get_formatted_address,
            get_last_location,
            show_location_permission_dialog
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
