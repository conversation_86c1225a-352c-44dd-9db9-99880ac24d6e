{"$schema": "https://schema.tauri.app/config/2", "productName": "automotive-ui", "version": "0.1.0", "identifier": "com.automotive-ui.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "automotive-ui", "width": 800, "height": 600}], "security": {"csp": "default-src 'self'; connect-src 'self' https://api.example.com; geolocation 'self'"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}